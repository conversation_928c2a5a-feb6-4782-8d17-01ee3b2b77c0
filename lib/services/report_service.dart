import '../models/meter_reading.dart';
import '../models/meter_type.dart';

class ReportService {
  /// Generate monthly report data for a specific year
  Map<String, dynamic> generateMonthlyReport(
    List<MeterReading> readings,
    int year,
  ) {
    final yearReadings =
        readings.where((r) => r.timestamp.year == year).toList();

    // Process readings separately for water and electrical meters

    final monthlyData = <int, Map<String, dynamic>>{};
    final monthlyWaterConsumption = <int, double>{};
    final monthlyElectricalConsumption = <int, Map<MeterType, double>>{};
    final monthlyReadingCounts = <int, int>{};

    // Initialize months
    for (int month = 1; month <= 12; month++) {
      monthlyData[month] = {
        'readings': <MeterReading>[],
        'totalReadings': 0,
        'waterConsumption': 0.0,
        'electricalConsumption': <MeterType, double>{},
        'averageWaterConsumption': 0.0,
        'averageElectricalConsumption': <MeterType, double>{},
        'buildings': <String>{},
        'rooms': <String>{},
      };
      monthlyWaterConsumption[month] = 0.0;
      monthlyElectricalConsumption[month] = {};
      monthlyReadingCounts[month] = 0;
    }

    // Process readings by month
    for (final reading in yearReadings) {
      final month = reading.timestamp.month;
      monthlyData[month]!['readings'].add(reading);
      monthlyReadingCounts[month] = monthlyReadingCounts[month]! + 1;

      // Track consumption separately for water and electrical
      if (reading.meterType == MeterType.water) {
        monthlyWaterConsumption[month] =
            monthlyWaterConsumption[month]! + reading.readingValue;
      } else {
        // Electrical meters (main, generator, utility)
        if (!monthlyElectricalConsumption[month]!.containsKey(
          reading.meterType,
        )) {
          monthlyElectricalConsumption[month]![reading.meterType] = 0;
        }
        monthlyElectricalConsumption[month]![reading.meterType] =
            monthlyElectricalConsumption[month]![reading.meterType]! +
            reading.readingValue;
      }

      // Track buildings and rooms
      (monthlyData[month]!['buildings'] as Set<String>).add(
        reading.buildingNumber,
      );
      (monthlyData[month]!['rooms'] as Set<String>).add(
        '${reading.buildingNumber}-${reading.roomNumber}',
      );
    }

    // Calculate statistics
    for (int month = 1; month <= 12; month++) {
      final readings = monthlyData[month]!['readings'] as List<MeterReading>;
      monthlyData[month]!['totalReadings'] = readings.length;

      // Water consumption
      final waterConsumption = monthlyWaterConsumption[month]!;
      final waterReadingsCount =
          readings.where((r) => r.meterType == MeterType.water).length;
      monthlyData[month]!['waterConsumption'] = waterConsumption;
      monthlyData[month]!['averageWaterConsumption'] =
          waterReadingsCount > 0 ? waterConsumption / waterReadingsCount : 0.0;

      // Electrical consumption by type
      for (final meterType in MeterType.values) {
        if (meterType == MeterType.water) continue;

        final consumption =
            monthlyElectricalConsumption[month]![meterType] ?? 0.0;
        final typeReadings =
            readings.where((r) => r.meterType == meterType).length;

        monthlyData[month]!['electricalConsumption'][meterType] = consumption;
        monthlyData[month]!['averageElectricalConsumption'][meterType] =
            typeReadings > 0 ? consumption / typeReadings : 0.0;
      }
    }

    return {
      'year': year,
      'monthlyData': monthlyData,
      'totalReadings': yearReadings.length,
      'totalBuildings':
          yearReadings.map((r) => r.buildingNumber).toSet().length,
      'totalRooms':
          yearReadings
              .map((r) => '${r.buildingNumber}-${r.roomNumber}')
              .toSet()
              .length,
      'yearlyConsumption': _calculateYearlyConsumption(yearReadings),
      'monthlyTrends': _calculateMonthlyTrends(monthlyData),
      'peakWaterMonth': _findPeakWaterMonth(monthlyWaterConsumption),
      'peakElectricalMonth': _findPeakElectricalMonth(
        monthlyElectricalConsumption,
      ),
      'averageMonthlyReadings': yearReadings.length / 12,
    };
  }

  /// Generate yearly comparison report
  Map<String, dynamic> generateYearlyReport(List<MeterReading> readings) {
    final yearlyData = <int, Map<String, dynamic>>{};
    final availableYears =
        readings.map((r) => r.timestamp.year).toSet().toList()..sort();

    for (final year in availableYears) {
      final yearReadings =
          readings.where((r) => r.timestamp.year == year).toList();

      yearlyData[year] = {
        'totalReadings': yearReadings.length,
        'totalConsumption': _calculateYearlyConsumption(yearReadings),
        'averageConsumption': _calculateAverageConsumption(yearReadings),
        'buildings': yearReadings.map((r) => r.buildingNumber).toSet().length,
        'rooms':
            yearReadings
                .map((r) => '${r.buildingNumber}-${r.roomNumber}')
                .toSet()
                .length,
        'monthlyAverage': yearReadings.length / 12,
        'peakMonth': _findPeakMonthForYear(yearReadings),
        'growthRate': 0.0, // Will be calculated below
      };
    }

    // Calculate year-over-year growth rates
    for (int i = 1; i < availableYears.length; i++) {
      final currentYear = availableYears[i];
      final previousYear = availableYears[i - 1];

      final currentTotal = _getTotalConsumptionValue(
        yearlyData[currentYear]!['totalConsumption'],
      );
      final previousTotal = _getTotalConsumptionValue(
        yearlyData[previousYear]!['totalConsumption'],
      );

      if (previousTotal > 0) {
        final growthRate =
            ((currentTotal - previousTotal) / previousTotal) * 100;
        yearlyData[currentYear]!['growthRate'] = growthRate;
      }
    }

    return {
      'availableYears': availableYears,
      'yearlyData': yearlyData,
      'totalReadings': readings.length,
      'dateRange': {
        'start':
            readings.isNotEmpty
                ? readings
                    .map((r) => r.timestamp)
                    .reduce((a, b) => a.isBefore(b) ? a : b)
                : null,
        'end':
            readings.isNotEmpty
                ? readings
                    .map((r) => r.timestamp)
                    .reduce((a, b) => a.isAfter(b) ? a : b)
                : null,
      },
      'overallTrends': _calculateOverallTrends(yearlyData),
    };
  }

  /// Generate building comparison report
  Map<String, dynamic> generateBuildingReport(List<MeterReading> readings) {
    final buildingData = <String, Map<String, dynamic>>{};
    final buildings = readings.map((r) => r.buildingNumber).toSet();

    for (final building in buildings) {
      final buildingReadings =
          readings.where((r) => r.buildingNumber == building).toList();

      buildingData[building] = {
        'totalReadings': buildingReadings.length,
        'rooms': buildingReadings.map((r) => r.roomNumber).toSet().length,
        'totalConsumption': _calculateYearlyConsumption(buildingReadings),
        'averageConsumption': _calculateAverageConsumption(buildingReadings),
        'meterTypes': buildingReadings.map((r) => r.meterType).toSet().length,
        'lastReading':
            buildingReadings.isNotEmpty
                ? buildingReadings
                    .reduce((a, b) => a.timestamp.isAfter(b.timestamp) ? a : b)
                    .timestamp
                : null,
        'efficiency': _calculateBuildingEfficiency(buildingReadings),
      };
    }

    return {
      'buildingData': buildingData,
      'totalBuildings': buildings.length,
      'mostEfficientBuilding': _findMostEfficientBuilding(buildingData),
      'highestConsumptionBuilding': _findHighestConsumptionBuilding(
        buildingData,
      ),
      'averageRoomsPerBuilding':
          buildingData.values
              .map((data) => data['rooms'] as int)
              .reduce((a, b) => a + b) /
          buildings.length,
    };
  }

  /// Generate consumption trends and predictions
  Map<String, dynamic> generateConsumptionTrends(List<MeterReading> readings) {
    // Process readings separately for water (m³) and electrical (kWh) meters

    final monthlyWaterTotals = <DateTime, double>{};
    final monthlyElectricalTotals = <DateTime, double>{};
    final electricalMeterTypeTrends = <MeterType, List<double>>{};

    // Group readings by month
    final groupedByMonth = <String, List<MeterReading>>{};
    for (final reading in readings) {
      final monthKey =
          '${reading.timestamp.year}-${reading.timestamp.month.toString().padLeft(2, '0')}';
      groupedByMonth.putIfAbsent(monthKey, () => []).add(reading);
    }

    // Calculate monthly totals separately for water and electrical
    for (final entry in groupedByMonth.entries) {
      final monthReadings = entry.value;
      final date = DateTime.parse('${entry.key}-01');

      // Water consumption (m³)
      final waterConsumption = monthReadings
          .where((r) => r.meterType == MeterType.water)
          .fold<double>(0, (sum, r) => sum + r.readingValue);
      monthlyWaterTotals[date] = waterConsumption;

      // Electrical consumption (kWh)
      final electricalConsumption = monthReadings
          .where((r) => r.meterType != MeterType.water)
          .fold<double>(0, (sum, r) => sum + r.readingValue);
      monthlyElectricalTotals[date] = electricalConsumption;
    }

    // Calculate trends by electrical meter type
    for (final meterType in MeterType.values) {
      if (meterType == MeterType.water) continue;

      final monthlyValues = <double>[];
      for (final entry in groupedByMonth.entries) {
        final monthTypeReadings = entry.value.where(
          (r) => r.meterType == meterType,
        );
        final monthTotal = monthTypeReadings.fold<double>(
          0,
          (sum, r) => sum + r.readingValue,
        );
        monthlyValues.add(monthTotal);
      }
      electricalMeterTypeTrends[meterType] = monthlyValues;
    }

    return {
      'monthlyWaterTotals': monthlyWaterTotals,
      'monthlyElectricalTotals': monthlyElectricalTotals,
      'electricalMeterTypeTrends': electricalMeterTypeTrends,
      'waterTrendDirection': _calculateTrendDirection(
        monthlyWaterTotals.values.toList(),
      ),
      'electricalTrendDirection': _calculateTrendDirection(
        monthlyElectricalTotals.values.toList(),
      ),
      'waterSeasonalPatterns': _identifySeasonalPatterns(monthlyWaterTotals),
      'electricalSeasonalPatterns': _identifySeasonalPatterns(
        monthlyElectricalTotals,
      ),
      'waterPredictions': _generatePredictions(
        monthlyWaterTotals.values.toList(),
      ),
      'electricalPredictions': _generatePredictions(
        monthlyElectricalTotals.values.toList(),
      ),
    };
  }

  // Private helper methods

  Map<MeterType, double> _calculateYearlyConsumption(
    List<MeterReading> readings,
  ) {
    final consumption = <MeterType, double>{};
    for (final meterType in MeterType.values) {
      consumption[meterType] = readings
          .where((r) => r.meterType == meterType)
          .fold<double>(0, (sum, r) => sum + r.readingValue);
    }
    return consumption;
  }

  Map<MeterType, double> _calculateAverageConsumption(
    List<MeterReading> readings,
  ) {
    final averages = <MeterType, double>{};
    for (final meterType in MeterType.values) {
      final typeReadings =
          readings.where((r) => r.meterType == meterType).toList();
      averages[meterType] =
          typeReadings.isNotEmpty
              ? typeReadings.fold<double>(0, (sum, r) => sum + r.readingValue) /
                  typeReadings.length
              : 0;
    }
    return averages;
  }

  Map<String, dynamic> _calculateMonthlyTrends(
    Map<int, Map<String, dynamic>> monthlyData,
  ) {
    final trends = <String, List<double>>{};

    // Water trends (m³)
    final waterValues = <double>[];
    for (int month = 1; month <= 12; month++) {
      final consumption = monthlyData[month]!['waterConsumption'] ?? 0.0;
      waterValues.add(consumption);
    }
    trends['Water (m³)'] = waterValues;

    // Electrical trends (kWh) by meter type
    for (final meterType in MeterType.values) {
      if (meterType == MeterType.water) continue;

      final monthlyValues = <double>[];
      for (int month = 1; month <= 12; month++) {
        final electricalData =
            monthlyData[month]!['electricalConsumption']
                as Map<MeterType, double>?;
        final consumption = electricalData?[meterType] ?? 0.0;
        monthlyValues.add(consumption);
      }
      trends['${meterType.displayName} (kWh)'] = monthlyValues;
    }

    return trends;
  }

  Map<String, dynamic> _findPeakWaterMonth(
    Map<int, double> monthlyWaterConsumption,
  ) {
    double maxConsumption = 0;
    int peakMonth = 1;

    for (int month = 1; month <= 12; month++) {
      final consumption = monthlyWaterConsumption[month] ?? 0.0;
      if (consumption > maxConsumption) {
        maxConsumption = consumption;
        peakMonth = month;
      }
    }

    return {'month': peakMonth, 'consumption': maxConsumption, 'unit': 'm³'};
  }

  Map<String, dynamic> _findPeakElectricalMonth(
    Map<int, Map<MeterType, double>> monthlyElectricalConsumption,
  ) {
    double maxConsumption = 0;
    int peakMonth = 1;
    MeterType peakMeterType = MeterType.main;

    for (int month = 1; month <= 12; month++) {
      final monthData =
          monthlyElectricalConsumption[month] ?? <MeterType, double>{};
      for (final entry in monthData.entries) {
        if (entry.value > maxConsumption) {
          maxConsumption = entry.value;
          peakMonth = month;
          peakMeterType = entry.key;
        }
      }
    }

    return {
      'month': peakMonth,
      'meterType': peakMeterType,
      'consumption': maxConsumption,
      'unit': 'kWh',
    };
  }

  String _findPeakMonthForYear(List<MeterReading> yearReadings) {
    final monthlyTotals = <int, double>{};

    for (final reading in yearReadings) {
      final month = reading.timestamp.month;
      monthlyTotals[month] = (monthlyTotals[month] ?? 0) + reading.readingValue;
    }

    if (monthlyTotals.isEmpty) return 'N/A';

    final peakMonth =
        monthlyTotals.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    const monthNames = [
      '',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return monthNames[peakMonth];
  }

  double _getTotalConsumptionValue(Map<MeterType, double> consumption) {
    return consumption.values.fold<double>(0, (sum, value) => sum + value);
  }

  Map<String, dynamic> _calculateOverallTrends(
    Map<int, Map<String, dynamic>> yearlyData,
  ) {
    final years = yearlyData.keys.toList()..sort();
    if (years.length < 2) return {'trend': 'insufficient_data'};

    final totalConsumptions =
        years
            .map(
              (year) => _getTotalConsumptionValue(
                yearlyData[year]!['totalConsumption'],
              ),
            )
            .toList();

    final avgGrowthRate = _calculateAverageGrowthRate(totalConsumptions);

    return {
      'trend':
          avgGrowthRate > 5
              ? 'increasing'
              : avgGrowthRate < -5
              ? 'decreasing'
              : 'stable',
      'averageGrowthRate': avgGrowthRate,
      'totalGrowth':
          totalConsumptions.isNotEmpty && totalConsumptions.first > 0
              ? ((totalConsumptions.last - totalConsumptions.first) /
                      totalConsumptions.first) *
                  100
              : 0,
    };
  }

  double _calculateBuildingEfficiency(List<MeterReading> buildingReadings) {
    if (buildingReadings.isEmpty) return 0;

    final totalConsumption = buildingReadings.fold<double>(
      0,
      (sum, r) => sum + r.readingValue,
    );
    final rooms = buildingReadings.map((r) => r.roomNumber).toSet().length;

    return rooms > 0 ? totalConsumption / rooms : 0;
  }

  String _findMostEfficientBuilding(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    if (buildingData.isEmpty) return 'N/A';

    return buildingData.entries
        .reduce(
          (a, b) =>
              (a.value['efficiency'] as double) <
                      (b.value['efficiency'] as double)
                  ? a
                  : b,
        )
        .key;
  }

  String _findHighestConsumptionBuilding(
    Map<String, Map<String, dynamic>> buildingData,
  ) {
    if (buildingData.isEmpty) return 'N/A';

    return buildingData.entries
        .reduce(
          (a, b) =>
              _getTotalConsumptionValue(a.value['totalConsumption']) >
                      _getTotalConsumptionValue(b.value['totalConsumption'])
                  ? a
                  : b,
        )
        .key;
  }

  String _calculateTrendDirection(List<double> values) {
    if (values.length < 2) return 'insufficient_data';

    final firstHalf = values.take(values.length ~/ 2).toList();
    final secondHalf = values.skip(values.length ~/ 2).toList();

    final firstAvg =
        firstHalf.fold<double>(0, (sum, v) => sum + v) / firstHalf.length;
    final secondAvg =
        secondHalf.fold<double>(0, (sum, v) => sum + v) / secondHalf.length;

    final change = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (change > 10) return 'increasing';
    if (change < -10) return 'decreasing';
    return 'stable';
  }

  Map<String, double> _identifySeasonalPatterns(
    Map<DateTime, double> monthlyTotals,
  ) {
    final seasonalTotals = <String, double>{'Summer': 0, 'Cold': 0, 'Rain': 0};
    final seasonalCounts = <String, int>{'Summer': 0, 'Cold': 0, 'Rain': 0};

    for (final entry in monthlyTotals.entries) {
      final month = entry.key.month;
      final season = _getSeason(month);
      seasonalTotals[season] = seasonalTotals[season]! + entry.value;
      seasonalCounts[season] = seasonalCounts[season]! + 1;
    }

    // Calculate averages
    final seasonalAverages = <String, double>{};
    for (final season in seasonalTotals.keys) {
      seasonalAverages[season] =
          seasonalCounts[season]! > 0
              ? seasonalTotals[season]! / seasonalCounts[season]!
              : 0;
    }

    return seasonalAverages;
  }

  String _getSeason(int month) {
    // Summer: March to May (hot season)
    if (month >= 3 && month <= 5) return 'Summer';
    // Rain: June to October (rainy season)
    if (month >= 6 && month <= 10) return 'Rain';
    // Cold: November to February (cool season)
    return 'Cold';
  }

  List<double> _generatePredictions(List<double> historicalData) {
    if (historicalData.length < 3) return [];

    // Simple linear regression for prediction
    final n = historicalData.length;
    final x = List.generate(n, (i) => i.toDouble());
    final y = historicalData;

    final sumX = x.fold<double>(0, (sum, val) => sum + val);
    final sumY = y.fold<double>(0, (sum, val) => sum + val);
    final sumXY = List.generate(
      n,
      (i) => x[i] * y[i],
    ).fold<double>(0, (sum, val) => sum + val);
    final sumX2 = x.fold<double>(0, (sum, val) => sum + val * val);

    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    final intercept = (sumY - slope * sumX) / n;

    // Generate next 3 months predictions
    return List.generate(3, (i) => slope * (n + i) + intercept);
  }

  double _calculateAverageGrowthRate(List<double> values) {
    if (values.length < 2) return 0;

    double totalGrowthRate = 0;
    int validPairs = 0;

    for (int i = 1; i < values.length; i++) {
      if (values[i - 1] > 0) {
        final growthRate = ((values[i] - values[i - 1]) / values[i - 1]) * 100;
        totalGrowthRate += growthRate;
        validPairs++;
      }
    }

    return validPairs > 0 ? totalGrowthRate / validPairs : 0;
  }
}
